spring:
  application:
    name: user-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
      config:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
        # 扩展配置
        extension-configs:
          - data-id: redis-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: mysql-config.yml
            group: DEFAULT_GROUP
            refresh: true
