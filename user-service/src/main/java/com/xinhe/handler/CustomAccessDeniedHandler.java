package com.xinhe.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 自定义权限不足处理器
 */
@Component
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    // JSON 序列化器
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException, ServletException {
        // 设置响应头
        response.setContentType("application/json;charset=UTF-8");
        // 设置响应状态码
        response.setStatus(HttpStatus.FORBIDDEN.value());
        // 创建错误信息
        Map<String,Object> error = new HashMap<>();
        // 设置错误码
        error.put("code",HttpStatus.FORBIDDEN.value());
        // 设置错误消息
        error.put("message","权限不足，请联系管理员");
        // 设置错误路径
        error.put("path", request.getRequestURI());
        // 写入响应体
        response.getWriter().write(objectMapper.writeValueAsString(error));
    }
}
