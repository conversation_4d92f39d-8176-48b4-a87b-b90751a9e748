package com.xinhe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinhe.custom.CustomUserDetails;
import com.xinhe.mapper.TUserMapper;
import com.xinhe.pojo.TUser;
import com.xinhe.service.TUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class TUserServiceImpl extends ServiceImpl<TUserMapper, TUser> implements TUserService,UserDetailsService {

    private static final Logger log = LoggerFactory.getLogger(TUserServiceImpl.class);

    @Autowired
    private TUserMapper tUserMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.info("正在加载用户信息: {}", username);

        TUser tUser = tUserMapper.selectOne(new QueryWrapper<TUser>().eq("username", username));
        if (tUser == null) {
            log.warn("用户不存在: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        // 检查用户状态
        if (tUser.getStatus() == null || tUser.getStatus() != 1) {
            log.warn("用户已被禁用: {}", username);
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }

        // 构建用户权限
        List<GrantedAuthority> authorities = buildUserAuthorities(tUser);

        log.info("用户信息加载成功: {}, 权限数量: {}", username, authorities.size());
        return new CustomUserDetails(tUser, authorities);
    }

    /**
     * 构建用户权限
     * @param user 用户信息
     * @return 权限列表
     */
    private List<GrantedAuthority> buildUserAuthorities(TUser user) {
        List<GrantedAuthority> authorities = new ArrayList<>();

        // 基础用户权限
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

        // 根据用户ID或其他条件添加管理员权限
        if (user.getId() != null && user.getId() == 1L) {
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        }

        // 可以根据实际业务需求从数据库查询用户角色和权限
        // 这里简化处理，实际项目中应该有专门的角色权限表

        return authorities;
    }
}
