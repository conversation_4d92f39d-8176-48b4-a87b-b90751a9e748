package com.xinhe.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 阿里云OSS服务类
 * 基于STS临时授权实现文件上传下载
 */
@Service
public class OssService {

//    @Autowired
//    private OSSClient ossClient;  // OSS客户端
//
//    @Autowired
//    private StsService stsService;  // STS服务
//
//    /**
//     * 生成STS临时授权凭证
//     * @param userId 用户ID，用于权限控制
//     * @return STS临时凭证
//     */
//    public StsToken getStsToken(Long userId) {
//        // 1. 根据用户ID生成权限策略（最小权限原则）
//        String policy = generatePolicy(userId);
//
//        // 2. 调用STS服务获取临时凭证
//        return stsService.assumeRole(policy, 3600);  // 有效期1小时
//    }
//
//    /**
//     * 生成OSS上传签名URL
//     * @param objectName 对象名称
//     * @param userId 用户ID
//     * @return 带签名的上传URL
//     */
//    public String generateUploadUrl(String objectName, Long userId) {
//        // 1. 验证用户权限（示例：只能上传到自己的目录）
//        if (!objectName.startsWith("user/" + userId + "/")) {
//            throw new AccessDeniedException("没有权限上传到该目录");
//        }
//
//        // 2. 生成签名URL，有效期30分钟
//        Date expiration = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
//        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
//                "prod-bucket", objectName, HttpMethod.PUT);
//        request.setExpiration(expiration);
//
//        // 3. 设置内容类型
//        request.setContentType("application/octet-stream");
//
//        return ossClient.generatePresignedUrl(request).toString();
//    }
//
//    /*** 生成用户权限策略
//     * @param userId 用户ID
//     * @return 权限策略JSON
//     */
//    private String generatePolicy(Long userId) {
//        // 仅允许访问用户自己的目录
//        return "{\n" +
//                "  \"Version\": \"1\",\n" +
//                "  \"Statement\": [\n" +
//                "    {\n" +
//                "      \"Effect\": \"Allow\",\n" +
//                "      \"Action\": [\"oss:PutObject\", \"oss:GetObject\"],\n" +
//                "      \"Resource\": [\"acs:oss:*:*:prod-bucket/user/" + userId + "/*\"]\n" +
//                "    }\n" +
//                "  ]\n" +
//                "}";
//    }
}
