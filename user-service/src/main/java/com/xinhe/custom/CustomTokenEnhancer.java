package com.xinhe.custom;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 自定义JWT令牌增强器
 * 用于向JWT令牌中添加额外的用户信息，如用户ID、角色等
 */
public class CustomTokenEnhancer implements TokenEnhancer {

    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        DefaultOAuth2AccessToken token = (DefaultOAuth2AccessToken) accessToken;

        // 添加自定义信息到JWT载荷
        Map<String, Object> additionalInfo = new HashMap<>();
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();

        // 添加用户ID（需自定义UserDetails实现）
        if (userDetails instanceof CustomUserDetails) {
            additionalInfo.put("userId", ((CustomUserDetails) userDetails).getId());
        }

        // 添加用户名和角色信息
        additionalInfo.put("username", userDetails.getUsername());
//        additionalInfo.put("roles", userDetails.getAuthorities().stream()
//                .map(GrantedAuthority::getAuthority)
//                .collect(Collectors.toList()));

        // 添加令牌签发时间和过期时间
        additionalInfo.put("iat", System.currentTimeMillis() / 1000);
        additionalInfo.put("exp", token.getExpiration().getTime() / 1000);

        token.setAdditionalInformation(additionalInfo);
        return token;
    }
}
