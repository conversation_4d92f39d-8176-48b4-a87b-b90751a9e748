package com.xinhe.custom;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    // JSON 序列化器
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        // 设置响应头
        response.setContentType("application/json;charset=UTF-8");
        // 设置响应状态码
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        // 创建错误信息
        Map<String, Object> error = new HashMap<>();
        // 设置错误码
        error.put("code", HttpStatus.UNAUTHORIZED.value());
        // 设置错误消息
        error.put("message", "未授权，请先登录");
        // 设置错误路径
        error.put("path", request.getRequestURI());
        // 写入响应体
        response.getWriter().write(objectMapper.writeValueAsString(error));
    }
}
