package com.xinhe.config;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Properties;

/**
 * Nacos配置服务类
 * 用于从Nacos配置中心获取配置信息
 */
@Component
public class NacosConfigService {

    private static final Logger log = LoggerFactory.getLogger(NacosConfigService.class);

    @Value("${spring.cloud.nacos.discovery.server-addr:localhost:8848}")
    private String serverAddr;

    private ConfigService configService;

    @PostConstruct
    public void init() {
        try {
            Properties properties = new Properties();
            properties.put("serverAddr", serverAddr);
            configService = NacosFactory.createConfigService(properties);
            log.info("Nacos配置服务初始化成功，服务器地址: {}", serverAddr);
        } catch (NacosException e) {
            log.error("Nacos配置服务初始化失败", e);
            throw new RuntimeException("Nacos配置服务初始化失败", e);
        }
    }

    /**
     * 获取配置信息
     * @param dataId 配置ID
     * @param group 配置组
     * @param timeoutMs 超时时间（毫秒）
     * @return 配置内容
     */
    public String getConfig(String dataId, String group, long timeoutMs) {
        try {
            String config = configService.getConfig(dataId, group, timeoutMs);
            if (config == null || config.trim().isEmpty()) {
                log.warn("从Nacos获取配置为空，dataId: {}, group: {}, 使用默认值", dataId, group);
                return getDefaultConfig(dataId);
            }
            log.debug("从Nacos获取配置成功，dataId: {}, group: {}", dataId, group);
            return config;
        } catch (NacosException e) {
            log.error("从Nacos获取配置失败，dataId: {}, group: {}, 使用默认值", dataId, group, e);
            return getDefaultConfig(dataId);
        }
    }

    /**
     * 获取默认配置
     * @param dataId 配置ID
     * @return 默认配置值
     */
    private String getDefaultConfig(String dataId) {
        switch (dataId) {
            case "jwt.signing.key":
                return "seckill-jwt-secret-key-2025"; // 默认JWT签名密钥
            case "oauth.client.secret":
                return "client_secret"; // 默认客户端密钥
            default:
                log.warn("未找到配置项 {} 的默认值", dataId);
                return "";
        }
    }

    /**
     * 添加配置监听器
     * @param dataId 配置ID
     * @param group 配置组
     * @param listener 监听器
     */
    public void addListener(String dataId, String group, com.alibaba.nacos.api.config.listener.Listener listener) {
        try {
            configService.addListener(dataId, group, listener);
            log.info("添加Nacos配置监听器成功，dataId: {}, group: {}", dataId, group);
        } catch (NacosException e) {
            log.error("添加Nacos配置监听器失败，dataId: {}, group: {}", dataId, group, e);
        }
    }
}
