package com.xinhe.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;

/**
 * <AUTHOR>
 * 资源服务器配置
 * 实现 JWT 验证与权限控制
 */
@Configuration
// 开启资源服务器
@EnableResourceServer
// 开启方法级别权限控制
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ResourceServiceConfig extends ResourceServerConfigurerAdapter {

    private static final String RESOURCE_ID = "resource-server";

    @Value("${oauth.check-token-url:http://localhost:9999/oauth/check_token}")
    private String checkTokenUrl;

    @Value("${oauth.client-id:seckill-client}")
    private String clientId;

    @Value("${oauth.client-secret:client_secret}")
    private String clientSecret;

    @Autowired
    private NacosConfigService nacosConfigService;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        // 配置资源访问权限
        http.authorizeRequests()
                // 公共接口允许访问
                .antMatchers("/api/user/register", "/api/user/health").permitAll()
                .antMatchers("/oauth/**").permitAll()
                .antMatchers("/actuator/health").permitAll()
                // 用户接口需要USER权限
                .antMatchers("/api/user/profile").hasRole("USER")
                // 管理员接口需要ADMIN权限
                .antMatchers("/api/user/admin/**").hasRole("ADMIN")
                // 其他私有资源需要认证
                .anyRequest().authenticated();
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources
                // 设置资源ID
                .resourceId(RESOURCE_ID)
                // 设置令牌存储
                .tokenStore(jwtTokenStore());
    }

    /**
     * JWT令牌存储
     */
    @Bean
    public TokenStore jwtTokenStore() {
        return new JwtTokenStore(jwtAccessTokenConverter());
    }

    /**
     * JWT访问令牌转换器
     */
    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
        // 从Nacos获取JWT签名密钥
        String signingKey = nacosConfigService.getConfig("jwt.signing.key", "DEFAULT_GROUP", 5000);
        converter.setSigningKey(signingKey);
        return converter;
    }

    /**
     * 远程令牌服务（备用方案）
     */
    @Bean
    public RemoteTokenServices remoteTokenServices() {
        RemoteTokenServices tokenServices = new RemoteTokenServices();
        tokenServices.setCheckTokenEndpointUrl(checkTokenUrl);
        tokenServices.setClientId(clientId);
        tokenServices.setClientSecret(clientSecret);
        return tokenServices;
    }
}
