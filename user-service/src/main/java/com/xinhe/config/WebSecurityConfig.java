package com.xinhe.config;

import com.xinhe.custom.CustomAuthenticationEntryPoint;
import com.xinhe.handler.CustomAccessDeniedHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;

/**
 * <AUTHOR>
 * web 安全配置
 */

/**
 * Web安全配置类
 * 配置HTTP安全策略、密码编码器、认证管理器等
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)  // 启用方法级权限控制
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    // 用户详情服务
    @Autowired
    private UserDetailsService userDetailsService;

    // 自定义403处理器
    @Autowired
    private CustomAccessDeniedHandler accessDeniedHandler;

    // 自定义401处理器
    @Autowired
    private CustomAuthenticationEntryPoint authenticationEntryPoint;

    /**
     * 密码编码器，使用BCrypt加密算法
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        // 工作因子12，平衡安全性和性能
        return new BCryptPasswordEncoder(12);
    }

    /**
     * 配置认证管理器
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailsService)
                .passwordEncoder(passwordEncoder());
    }

    /**
     * 暴露AuthenticationManager供认证服务器使用
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * HTTP安全配置
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF保护，因为使用JWT无状态认证
                .csrf().disable()
                .sessionManagement()
                // 无状态会话
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .exceptionHandling()
                .accessDeniedHandler(accessDeniedHandler)
                .authenticationEntryPoint(authenticationEntryPoint)
                .and()
                .authorizeRequests()
                // OAuth端点允许访问
                .antMatchers("/oauth/**").permitAll()
                // 健康检查端点
                .antMatchers("/actuator/health").permitAll()
                // 用户注册端点允许访问
                .antMatchers("/api/user/register").permitAll()
                // 其他请求需要认证
                .anyRequest().authenticated();
    }
}
