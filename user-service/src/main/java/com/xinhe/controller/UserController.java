package com.xinhe.controller;

import com.xinhe.custom.CustomUserDetails;
import com.xinhe.pojo.TUser;
import com.xinhe.service.TUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 提供用户相关的API接口
 */
@RestController
@RequestMapping("/api/user")
@Api(tags = "用户管理")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private TUserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @ApiOperation("用户注册")
    public ResponseEntity<Map<String, Object>> register(@RequestBody TUser user) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查用户名是否已存在
            if (userService.lambdaQuery().eq(TUser::getUsername, user.getUsername()).exists()) {
                result.put("success", false);
                result.put("message", "用户名已存在");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 加密密码
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            user.setStatus(1); // 默认启用
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            
            // 保存用户
            userService.save(user);
            
            result.put("success", true);
            result.put("message", "注册成功");
            result.put("userId", user.getId());
            
            log.info("用户注册成功: {}", user.getUsername());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("用户注册失败", e);
            result.put("success", false);
            result.put("message", "注册失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    @ApiOperation("获取当前用户信息")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Map<String, Object>> getCurrentUser() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
                CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
                
                result.put("success", true);
                result.put("userId", userDetails.getId());
                result.put("username", userDetails.getUsername());
                result.put("authorities", userDetails.getAuthorities());
                
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "未找到用户信息");
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            result.put("success", false);
            result.put("message", "获取用户信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 管理员接口示例
     */
    @GetMapping("/admin/users")
    @ApiOperation("获取所有用户（管理员）")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getAllUsers() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("users", userService.list());
            result.put("total", userService.count());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            result.put("success", false);
            result.put("message", "获取用户列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "user-service");
        result.put("timestamp", new Date());
        return ResponseEntity.ok(result);
    }
}
