# User Service 认证服务

## 概述
这是一个基于Spring Security OAuth2和JWT的用户认证服务，支持用户注册、登录、权限管理等功能。

## 主要功能
- ✅ OAuth2认证服务器
- ✅ JWT令牌生成和验证
- ✅ 用户注册和登录
- ✅ 基于角色的权限控制
- ✅ Nacos配置中心集成
- ✅ Redis令牌存储
- ✅ 自定义异常处理

## 已修复的问题

### 1. Nacos配置中心集成
- ✅ 添加了`NacosConfigService`类
- ✅ 启用了Nacos配置依赖
- ✅ 创建了`bootstrap.yml`配置文件
- ✅ 修复了JWT签名密钥从Nacos获取的问题

### 2. 认证异常处理
- ✅ 修复了`CustomAuthenticationEntryPoint`的方法签名错误
- ✅ 完善了401和403异常处理

### 3. 安全配置优化
- ✅ 禁用了CSRF保护（JWT无状态认证不需要）
- ✅ 优化了资源服务器配置
- ✅ 添加了细粒度的权限控制

### 4. 用户服务增强
- ✅ 完善了`UserDetailsService`实现
- ✅ 添加了用户状态检查
- ✅ 实现了基于角色的权限分配

## 配置说明

### 数据库配置
1. 执行`src/main/resources/sql/oauth_client_details.sql`创建OAuth客户端表
2. 确保用户表`t_user`存在

### Nacos配置
在Nacos配置中心添加以下配置：

**配置项：jwt.signing.key**
```
seckill-jwt-secret-key-2025
```

**配置项：oauth.client.secret**
```
client_secret
```

## API接口

### 1. 用户注册
```http
POST /api/user/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456",
  "phone": "13800138000",
  "email": "<EMAIL>"
}
```

### 2. OAuth2登录
```http
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=password&username=testuser&password=123456&client_id=seckill-client&client_secret=client_secret
```

### 3. 获取用户信息
```http
GET /api/user/profile
Authorization: Bearer {access_token}
```

### 4. 管理员接口
```http
GET /api/user/admin/users
Authorization: Bearer {access_token}
```

## 权限说明

### 角色定义
- `ROLE_USER`: 普通用户权限
- `ROLE_ADMIN`: 管理员权限（用户ID为1的用户自动获得）

### 接口权限
- `/api/user/register`: 公开接口
- `/api/user/profile`: 需要`ROLE_USER`权限
- `/api/user/admin/**`: 需要`ROLE_ADMIN`权限

## 启动说明

1. 确保Nacos服务器运行在`localhost:8848`
2. 确保Redis服务器运行在`***************:6379`
3. 确保MySQL数据库可用
4. 启动应用：`mvn spring-boot:run`

## 测试流程

1. **注册用户**：调用注册接口创建用户
2. **获取令牌**：使用OAuth2密码模式获取访问令牌
3. **访问资源**：使用令牌访问受保护的资源
4. **权限测试**：测试不同角色的权限控制

## 注意事项

- 生产环境请修改默认的JWT签名密钥
- 建议使用HTTPS协议传输敏感信息
- 定期更新OAuth客户端密钥
- 监控令牌的使用情况和异常访问
