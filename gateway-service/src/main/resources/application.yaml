server:
  port: 8080
spring:
  application:
    name: gateway-service
  cloud:
#    nacos 注册中心
    nacos:
      discovery:
        server-addr: localhost:8848
    gateway:
      discovery:
        locator:
          lower-case-service-id: true
          enabled: true
      routes:
        # 这是认证服务路由，不需要过滤器
        - id: user-service
          # lb://user-service 为服务名
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
#            - Path=
#          filters:
#            - name: RequestRateLimiter
#              args:
#                redis-rate-limiter.replenishRate: 10
#                redis-rate-limiter.burstCapacity: 20
#        # 这是商品服务路由
#        - id: product-service
#          uri: lb://product-service
#          predicates:
#            - Path=/api/product/**
#          filters:
