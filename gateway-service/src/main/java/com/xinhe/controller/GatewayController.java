package com.xinhe.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网关测试控制器
 * 提供服务发现和健康检查接口
 */
@RestController
@RequestMapping("/gateway")
public class GatewayController {

    @Autowired
    private DiscoveryClient discoveryClient;

    /**
     * 获取所有服务列表
     */
    @GetMapping("/services")
    public Map<String, Object> getServices() {
        Map<String, Object> result = new HashMap<>();
        List<String> services = discoveryClient.getServices();
        result.put("services", services);
        result.put("count", services.size());
        return result;
    }

    /**
     * 获取指定服务的实例列表
     */
    @GetMapping("/services/{serviceName}")
    public Map<String, Object> getServiceInstances(@PathVariable String serviceName) {
        Map<String, Object> result = new HashMap<>();
        List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);
        result.put("serviceName", serviceName);
        result.put("instances", instances);
        result.put("count", instances.size());
        return result;
    }

    /**
     * 网关健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "gateway-service");
        result.put("port", 8080);
        return result;
    }
}
