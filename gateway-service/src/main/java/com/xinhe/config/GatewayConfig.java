package com.xinhe.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import reactor.core.publisher.Mono;

/**
 * 网关配置类
 * 提供路由配置和全局过滤器
 */
@Configuration
public class GatewayConfig {

    private static final Logger log = LoggerFactory.getLogger(GatewayConfig.class);

    /**
     * 自定义路由配置（可选，也可以使用application.yaml配置）
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // 用户服务路由
                .route("user-service-route", r -> r
                        .path("/api/user/**")
                        .uri("lb://user-service")
                )
                // OAuth认证路由
                .route("oauth-route", r -> r
                        .path("/oauth/**")
                        .uri("lb://user-service")
                )
                .build();
    }

    /**
     * 全局日志过滤器
     */
    @Bean
    public GlobalFilter loggingFilter() {
        return (exchange, chain) -> {
            String path = exchange.getRequest().getPath().value();
            String method = exchange.getRequest().getMethod().name();
            log.info("网关请求: {} {}", method, path);
            
            return chain.filter(exchange).then(Mono.fromRunnable(() -> {
                HttpStatus statusCode = exchange.getResponse().getStatusCode();
                log.info("网关响应: {} {} -> {}", method, path, statusCode);
            }));
        };
    }

    /**
     * 服务不可用处理过滤器
     */
    @Bean
    public GlobalFilter serviceUnavailableFilter() {
        return (exchange, chain) -> {
            return chain.filter(exchange)
                    .onErrorResume(throwable -> {
                        log.error("服务调用失败: {}", throwable.getMessage());
                        exchange.getResponse().setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
                        return exchange.getResponse().setComplete();
                    });
        };
    }
}
