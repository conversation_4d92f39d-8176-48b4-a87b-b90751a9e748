<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xinhe</groupId>
    <artifactId>seckill-frontend</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>gateway-service</module>
        <module>config-service</module>
        <module>user-service</module>
        <module>product-service</module>
        <module>seckill-service</module>
        <module>order-service</module>
        <module>payment-service</module>
        <module>registry-service</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- druid 数据源版本 -->
<!--        <druid.version>1.2.19</druid.version>-->
        <!-- druid spring boot starter 数据源版本 -->
        <druid.spring.boot.version>1.2.8</druid.spring.boot.version>
        <!-- mysql 版本 -->
        <mysql.version>8.0.28</mysql.version>
        <!-- mybatis 版本 -->
        <mybatis-plus-boot-starter.version>3.5.6</mybatis-plus-boot-starter.version>
        <!-- spring-boot 版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
        <!-- spring cloud 版本 -->
        <spring-cloud.version>2021.0.9</spring-cloud.version>
        <!-- spring cloud alibaba 版本 -->
        <spring-cloud-alibaba.version>2021.0.6.1</spring-cloud-alibaba.version>
<!--        &lt;!&ndash; redis 版本 &ndash;&gt;-->
<!--        <spring-boot-starter-data-redis.version>2.7.18</spring-boot-starter-data-redis.version>-->
        <!-- rabbitmq 版本 -->
        <spring-boot-starter-rabbitmq.version>2.7.18</spring-boot-starter-rabbitmq.version>
        <!-- lombok 版本 -->
        <lombok.version>1.18.30</lombok.version>
<!--        <seata.version>1.7.1</seata.version>-->
    </properties>
    <dependencyManagement>
        <!-- mysql -->
        <dependencies>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!-- druid 数据源 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.spring.boot.version}</version>
            </dependency>
            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <!-- spring-cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring-cloud-alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!-- spring-boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
